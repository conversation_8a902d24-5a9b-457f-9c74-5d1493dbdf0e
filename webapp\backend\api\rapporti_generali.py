from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, and_
from typing import List, Any, Optional
from datetime import datetime, date

from backend.database import get_db
from backend.models.user import User
from backend.models.cantiere import Cantiere
from backend.models.rapporto_generale_collaudo import RapportoGeneraleCollaudo
from backend.models.certificazione_cavo import CertificazioneCavo
from backend.schemas.rapporto_generale_collaudo import (
    RapportoGeneraleCollaudoCreate,
    RapportoGeneraleCollaudoUpdate,
    RapportoGeneraleCollaudoResponse,
    RapportoGeneraleCollaudoListResponse
)
from backend.auth import get_current_active_user

router = APIRouter()

@router.get("/{cantiere_id}/rapporti", response_model=List[RapportoGeneraleCollaudoListResponse])
def get_rapporti_cantiere(
    cantiere_id: int,
    skip: int = Query(0, ge=0, description="Numero di record da saltare"),
    limit: int = Query(100, ge=1, le=1000, description="Numero massimo di record da restituire"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Recupera tutti i rapporti generali di un cantiere.
    """
    # Verifica che il cantiere esista
    cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
    if not cantiere:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cantiere con ID {cantiere_id} non trovato"
        )

    # Query per ottenere i rapporti
    rapporti = db.query(RapportoGeneraleCollaudo).filter(
        RapportoGeneraleCollaudo.id_cantiere == cantiere_id
    ).offset(skip).limit(limit).all()

    return rapporti

@router.post("/{cantiere_id}/rapporti", response_model=RapportoGeneraleCollaudoResponse)
def create_rapporto(
    cantiere_id: int,
    rapporto_in: RapportoGeneraleCollaudoCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Crea un nuovo rapporto generale di collaudo.
    """
    # Verifica che il cantiere esista
    cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
    if not cantiere:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cantiere con ID {cantiere_id} non trovato"
        )

    # Verifica che il numero rapporto sia univoco
    existing_rapporto = db.query(RapportoGeneraleCollaudo).filter(
        RapportoGeneraleCollaudo.numero_rapporto == rapporto_in.numero_rapporto
    ).first()
    if existing_rapporto:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Rapporto con numero {rapporto_in.numero_rapporto} già esistente"
        )

    # Crea il nuovo rapporto
    rapporto = RapportoGeneraleCollaudo(
        id_cantiere=cantiere_id,
        **rapporto_in.dict()
    )
    
    db.add(rapporto)
    db.commit()
    db.refresh(rapporto)

    return rapporto

@router.get("/{cantiere_id}/rapporti/{rapporto_id}", response_model=RapportoGeneraleCollaudoResponse)
def get_rapporto(
    cantiere_id: int,
    rapporto_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Recupera i dettagli di un rapporto generale specifico.
    """
    rapporto = db.query(RapportoGeneraleCollaudo).filter(
        RapportoGeneraleCollaudo.id_rapporto == rapporto_id,
        RapportoGeneraleCollaudo.id_cantiere == cantiere_id
    ).first()

    if not rapporto:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Rapporto con ID {rapporto_id} non trovato nel cantiere {cantiere_id}"
        )

    return rapporto

@router.put("/{cantiere_id}/rapporti/{rapporto_id}", response_model=RapportoGeneraleCollaudoResponse)
def update_rapporto(
    cantiere_id: int,
    rapporto_id: int,
    rapporto_update: RapportoGeneraleCollaudoUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Aggiorna un rapporto generale esistente.
    """
    rapporto = db.query(RapportoGeneraleCollaudo).filter(
        RapportoGeneraleCollaudo.id_rapporto == rapporto_id,
        RapportoGeneraleCollaudo.id_cantiere == cantiere_id
    ).first()

    if not rapporto:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Rapporto con ID {rapporto_id} non trovato nel cantiere {cantiere_id}"
        )

    # Aggiorna i campi forniti
    update_data = rapporto_update.dict(exclude_unset=True)
    if update_data:
        for field, value in update_data.items():
            setattr(rapporto, field, value)
        
        rapporto.timestamp_modifica = datetime.now()
        db.commit()
        db.refresh(rapporto)

    return rapporto

@router.delete("/{cantiere_id}/rapporti/{rapporto_id}")
def delete_rapporto(
    cantiere_id: int,
    rapporto_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Elimina un rapporto generale.
    """
    rapporto = db.query(RapportoGeneraleCollaudo).filter(
        RapportoGeneraleCollaudo.id_rapporto == rapporto_id,
        RapportoGeneraleCollaudo.id_cantiere == cantiere_id
    ).first()

    if not rapporto:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Rapporto con ID {rapporto_id} non trovato nel cantiere {cantiere_id}"
        )

    db.delete(rapporto)
    db.commit()

    return {"message": "Rapporto eliminato con successo"}

@router.post("/{cantiere_id}/rapporti/{rapporto_id}/aggiorna-statistiche")
def aggiorna_statistiche_rapporto(
    cantiere_id: int,
    rapporto_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Aggiorna le statistiche del rapporto basandosi sulle certificazioni associate.
    """
    rapporto = db.query(RapportoGeneraleCollaudo).filter(
        RapportoGeneraleCollaudo.id_rapporto == rapporto_id,
        RapportoGeneraleCollaudo.id_cantiere == cantiere_id
    ).first()

    if not rapporto:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Rapporto con ID {rapporto_id} non trovato nel cantiere {cantiere_id}"
        )

    # Calcola le statistiche dalle certificazioni associate
    certificazioni_stats = db.query(
        func.count(CertificazioneCavo.id_certificazione).label('totali'),
        func.sum(func.case([(CertificazioneCavo.esito_complessivo == 'CONFORME', 1)], else_=0)).label('conformi'),
        func.sum(func.case([(CertificazioneCavo.esito_complessivo == 'NON_CONFORME', 1)], else_=0)).label('non_conformi')
    ).filter(
        CertificazioneCavo.id_rapporto == rapporto_id
    ).first()

    # Aggiorna il rapporto
    rapporto.numero_cavi_totali = certificazioni_stats.totali or 0
    rapporto.numero_cavi_conformi = certificazioni_stats.conformi or 0
    rapporto.numero_cavi_non_conformi = certificazioni_stats.non_conformi or 0
    rapporto.timestamp_modifica = datetime.now()

    db.commit()
    db.refresh(rapporto)

    return {
        "message": "Statistiche aggiornate con successo",
        "totali": rapporto.numero_cavi_totali,
        "conformi": rapporto.numero_cavi_conformi,
        "non_conformi": rapporto.numero_cavi_non_conformi
    }
